{"name": "localizify", "version": "2.0.0", "description": "Localize your messages easily", "main": "dist/localizify.min.js", "types": "index.d.ts", "scripts": {"format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "jest", "lint": "eslint \"src/**/*.ts\" \"test/**/*.ts\"", "build": "webpack"}, "repository": {"type": "git", "url": "git+https://github.com/noveogroup-amor<PERSON>ov/localizify.git"}, "keywords": ["javascript", "internationalization", "localization", "translation", "library", "nodejs", "i18n"], "author": "<PERSON> <<EMAIL>> (https://github.com/noveogroup-amor<PERSON><PERSON>)", "license": "MIT", "bugs": {"url": "https://github.com/noveogroup-amor<PERSON>ov/localizify/issues"}, "homepage": "https://github.com/noveogroup-amor<PERSON>ov/localizify#readme", "devDependencies": {"@babel/core": "7.8.7", "@babel/preset-env": "7.8.7", "@babel/preset-typescript": "7.8.3", "@types/jest": "25.1.4", "@types/node": "13.9.0", "@typescript-eslint/eslint-plugin": "^2.23.0", "@typescript-eslint/parser": "^2.23.0", "babel-loader": "8.0.6", "eslint": "6.8.0", "eslint-config-airbnb": "^18.1.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "husky": "4.2.3", "jest": "25.1.0", "prettier": "1.19.1", "ts-jest": "25.2.1", "ts-loader": "6.2.1", "typescript": "3.8.3", "uglifyjs-webpack-plugin": "2.2.0", "webpack": "4.42.0", "webpack-cli": "3.3.11"}, "dependencies": {}, "jest": {"transform": {".+\\.tsx?$": "ts-jest"}}}