!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.localizify=e():t.localizify=e()}("undefined"!=typeof window?window:this,function(){return n={},o.m=r=[function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0});var c,s,a=r(1),u=r(2);(s=c=e.EventTypes||(e.EventTypes={})).ChangeLocale="CHANGE_LOCALE",s.TranslationNotFound="TRANSLATION_NOT_FOUND";var l,p=(l=u.EventEmitter,o(f,l),f.prototype.getStore=function(){return this.store},f.prototype.getLocale=function(){return this.getStore().locale},f.prototype.setLocale=function(t){var e=this.getStore().locale;return this.isLocale(t)&&e!==t&&(this.getStore().locale=t,this.emit(c.ChangeLocale,t,e)),this},f.prototype.isLocale=function(t){return this.getStore().localesList.includes(t)},f.prototype.onLocaleChange=function(t){return this.on(c.ChangeLocale,t),t},f.prototype.onTranslationNotFound=function(t){this.on(c.TranslationNotFound,t)},f.prototype.setDefaultScope=function(t){return this.getStore().scope=t,this},f.prototype.clearDefaultScope=function(){return this.getStore().scope=null,this},f.prototype.registerInterpolations=function(t){return Object.assign(this.getStore().interpolations,t),this},f.prototype.add=function(t,e,r){var n=this.getStore(),o=e;return r&&((o={})[e]=r),this.isLocale(t)||n.localesList.push(t),n.translations[t]=i(i({},n.translations[t]),o),n.normalizedKeys[t]=i(i({},n.normalizedKeys[t]),a.normalize(o)),this},f.prototype.detectLocale=function(t){var e=t;if(a.isBrowser()&&!e&&(e=navigator.languages&&navigator.languages[0]||navigator.language||navigator.userLanguage),!e)return!1;var r=e.toLowerCase().split(/[_-]+/)[0];return!!this.isLocale(r)&&r},f.prototype.replaceData=function(t,n){var o=this,i=t;return(i.match(/\{(.*?)\}/gi)||[]).forEach(function(t){var e=t.replace(/[{}]/gi,""),r=n&&n[e]||o.getStore().interpolations[e]||t;i=i.replace(t,r)}),i},f.prototype.translate=function(t,e){if(void 0===e&&(e={}),!t)throw new Error('"key" param is required');var r=e.locale,n=e.scope,o=void 0===n?this.getStore().scope:n,i=this.isLocale(r)&&r||this.getStore().locale;if(!i)throw new Error("Current locale is not defined");var s=this.getStore().normalizedKeys[i],a=(o?o+".":"")+t,u=s&&s[a];return u||this.emit(c.TranslationNotFound,i,t,o),this.replaceData(u?s[a]:t,e)},f);function f(){var t=null!==l&&l.apply(this,arguments)||this;return t.store={locale:"en",localesList:["en"],scope:null,translations:{},interpolations:{},normalizedKeys:{},separator:"."},t}e.Localizify=p;var h=new(e.Instance=p),y=h.translate.bind(h);e.t=y,e.default=h},function(t,i,e){"use strict";var s=this&&this.__spreadArrays||function(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;var n=Array(t),o=0;for(e=0;e<r;e++)for(var i=arguments[e],s=0,a=i.length;s<a;s++,o++)n[o]=i[s];return n};Object.defineProperty(i,"__esModule",{value:!0});i.normalize=function(t,n,o){var e;return void 0===n&&(n=[]),void 0===o&&(o={}),"string"==typeof t?o[n.join(".")]=t:null!==(e=t)&&"[object Object]"===Object.prototype.toString.call(e)&&Object.entries(t).forEach(function(t){var e=t[0],r=t[1];return i.normalize(r,s(n,[e]),o)}),o},i.isBrowser=function(){return"undefined"!=typeof window&&window.document&&window.document.createElement}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=(o.prototype.getListeners=function(t){return this.listeners[t]||[]},o.prototype.on=function(t,e){if("function"!=typeof e)throw new TypeError("Listener must be a function");return-1===this.getListeners(t).indexOf(e)&&(this.hasListeners(t)||(this.listeners[t]=[]),this.listeners[t].push(e)),e},o.prototype.off=function(t,e){if(this.hasListeners(t)){var r=this.getListeners(t);this.listeners[t]=r.filter(function(t){return e!==t})}return this},o.prototype.emit=function(t){for(var e=this,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return this.getListeners(t).forEach(function(t){return t.apply(e,r)}),this},o.prototype.hasListeners=function(t){return 0<this.getListeners(t).length},o);function o(){this.listeners={}}e.EventEmitter=n}],o.c=n,o.d=function(t,e,r){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=0);function o(t){if(n[t])return n[t].exports;var e=n[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,o),e.l=!0,e.exports}var r,n});
