{"name": "bl", "version": "1.0.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "main": "bl.js", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "repository": {"type": "git", "url": "https://github.com/rvagg/bl.git"}, "homepage": "https://github.com/rvagg/bl", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "dependencies": {"readable-stream": "~2.0.0"}, "devDependencies": {"tape": "~2.12.3", "hash_file": "~0.1.1", "faucet": "~0.0.1", "brtapsauce": "~0.3.0"}}