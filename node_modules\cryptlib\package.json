{"name": "cryptlib", "version": "1.0.3", "author": {"name": "<PERSON>"}, "main": "dist/CryptLib.js", "description": "CryptLib uses AES 256 for encryption or decryption. This library can be used for encryption and decryption of strings using Node on iOS, Android and Windows", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"start": "node example.js", "test": "node node_modules/mocha/bin/mocha --reporter spec test/*"}, "repository": {"type": "git", "url": "git+ssh://**************:invalidred/cryptlib.git"}, "keywords": ["AES node", "Cross Platform", "Encryption", "Decryption"], "dependencies": {"bl": "1.0.0"}, "devDependencies": {"babel": "^5.2.17", "chai": "^2.3.0", "gulp": "^3.8.11", "gulp-babel": "^5.1.0", "gulp-mocha": "^2.0.1", "mocha": "^2.2.4"}, "engine": "node >= 0.10.x", "homepage": "https://github.com/invalidred/cryptlib", "bugs": {"url": "https://github.com/invalidred/cryptlib/issues"}, "private": false, "license": "MIT", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "invalidred", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}]}