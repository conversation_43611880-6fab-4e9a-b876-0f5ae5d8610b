{"name": "Validator", "version": "1.1.4", "description": "A JavaScript validation package, based on Laravel validation.", "main": "dist/Validator.js", "scripts": {"test": "nyc --check-coverage --lines 85 mocha --require @babel/register", "coverage": "npm run test && nyc report --reporter=text-lcov | coveralls", "test:watch": "nodemon --exec npm run test", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c", "watch": "rollup -c -w", "prepublishOnly": "npm run build && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/jfstn/Validator.git"}, "keywords": ["javascript", "validator", "laravel"], "author": "<PERSON>i <PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "joaofaus<PERSON>@protonmail.com"}], "license": "MIT", "homepage": "https://github.com/jfstn/Validator#readme", "devDependencies": {"@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/register": "^7.11.5", "chai": "^4.2.0", "coveralls": "^3.1.0", "mocha": "^8.1.3", "nodemon": "^2.0.4", "nyc": "^15.1.0", "rimraf": "^3.0.2", "rollup": "^2.27.0", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^4.0.1", "rollup-plugin-uglify": "^6.0.4"}}